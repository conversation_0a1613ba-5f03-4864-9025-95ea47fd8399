<!-- 
  制造业、科技型中小企业和专精特新企业放款Top1展示组件
  功能：展示三类重点企业的放款情况
  特点：
  1. 展示三类企业的放款金额和笔数
  2. 支持动态数据更新
  3. 响应式设计
  4. 美观的卡片式布局
  5. 渐变文字效果
-->
<template>
  <div class="Average-fwstjjClass">
    <!-- 头部标题区域 -->
    <div class="Average-headerImg">制造业·科技·专精特新放款Top1</div>
    <!-- 主体内容区域 -->
    <div class="Average-bottomClass">
      <div class="Average-apply-model-container">
        <!-- 卡片网格布局 -->
        <div class="Average-model-card-grid">
          <!-- 循环渲染企业类型卡片 -->
          <div
            class="Average-model-card"
            v-for="item in modelList"
            :key="item.type"
          >
            <!-- 企业类型名称 -->
            <div class="Average-model-card-type">{{ item.type }}</div>
            <!-- 企业类型图标 -->
            <div class="Average-model-card-img">
              <img :src="item.img" alt="" />
            </div>
            <!-- 放款金额 -->
            <div class="Average-model-card-amount">
              {{ item.amount }}<span class="Average-unit"></span>
            </div>
            <!-- 放款笔数 -->
            <div class="Average-model-card-count">
              {{ item.count }}<span class="Average-unit"></span>
            </div>
            <!-- 放款银行 -->
            <div class="Average-model-card-bank">{{ item.bank }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { productUpdateList } from "@/api/article.js";

export default {
  name: "ApplyModel",
  data() {
    return {
      selectedModel: "", // 选中的模型
      selectedDataType: "", // 选中的数据类型
      // 企业类型列表数据
      modelList: [
        {
          type: "制造业",
          img: require("../assets/tool.png"),
          amount: "1904.78",
          count: 123,
          bank: "建设银行",
        },
        {
          type: "科技型中小企业",
          img: require("../assets/Microscope.png"),
          amount: "1904.78",
          count: 123,
          bank: "建设银行",
        },
        {
          type: "专精特新企业",
          img: require("../assets/zhuan.png"),
          amount: "1904.78",
          count: 123,
          bank: "建设银行",
        },
      ],
    };
  },
  mounted() {
    // 获取金融机构放款重点行业数据
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "金融机构放款重点行业",
      },
    ]).then((res) => {
      // 解析返回的数据
      let list = JSON.parse(res.data.data[0].bizContent);
      // 更新企业类型列表
      this.modelList = list.map(item=>{
        return {
          type: item.industry,
          img: require("../assets/tool.png"),
          amount: item.loanAmt,
          count: item.loanSum,
          bank: item.financeName,
        }
      })
      // 设置特定企业类型的图标
      this.modelList[1].img = require("../assets/Microscope.png");
      this.modelList[2].img = require("../assets/zhuan.png");
    });
  },
  methods: {
    // 选择模型
    selectModel(model) {
      this.selectedModel = model;
      this.$emit("modelSelected", model);
    },
    // 选择数据类型
    selectDataType(type) {
      this.selectedDataType = type;
      this.$emit("dataTypeSelected", type);
    },
  },
};
</script>

<style scoped lang="scss">
/* 组件主容器样式 */
.Average-fwstjjClass {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部标题样式 */
  .Average-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    // 添加文字阴影效果
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  }

  /* 底部内容区域样式 */
  .Average-bottomClass {
    width: 100%;
    height: calc(27vh - 120px);
    flex: 1;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
  }
}

/* 卡片容器样式 */
.Average-apply-model-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  margin: 0px auto;

  /* 卡片网格布局 */
  .Average-model-card-grid {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    width: 100%;
    height: 100%;
  }

  /* 单个卡片样式 */
  .Average-model-card {
    display: flex;
    height: calc(27vh - 150px);
    flex-direction: column;
    justify-content: space-between;
    align-items: center;
    padding: 18px 0 12px 0;
    margin: 0 16px;
  }

  /* 卡片图标样式 */
  .Average-model-card-img img {
    width: 95px;
    height: 111.4px;
    margin-bottom: 8px;
  }

  /* 企业类型名称样式 */
  .Average-model-card-type {
    font-family: PangMenZhengDao;
    font-size: 36px;
    color: #ffffff;
    letter-spacing: 2px;
    text-align: center;
    font-style: normal;
    // 设置文字渐变效果
    background: linear-gradient(180deg, #ffffff 0%, #73bcff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  /* 放款金额样式 */
  .Average-model-card-amount {
    font-family: OPPOSans, OPPOSans;
    font-weight: normal;
    font-size: 32px;
    color: #ffffff;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;
    // 设置文字渐变效果
    background: linear-gradient(90deg, #ffffff 0%, #f3c376 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    .Average-unit {
      color: #fff;
      font-size: 16px;
      margin-left: 2px;
    }
  }

  /* 放款笔数样式 */
  .Average-model-card-count {
    font-family: OPPOSans, OPPOSans;
    font-weight: normal;
    font-size: 32px;
    color: #ffffff;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;
    // 设置文字渐变效果
    background: linear-gradient(90deg, #ffffff 0%, #4da3ff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    .Average-unit {
      color: #fff !important;
      font-size: 16px;
      margin-left: 2px;
    }
  }

  /* 放款银行样式 */
  .Average-model-card-bank {
    width: 170px;
    padding: 10px 10px;
    font-family: PingFangSC, PingFang SC;
    background: rgba(65, 158, 255, 0.11);
    color: #e2f0ff;
    line-height: 30px;
    font-size: 24px;
    border: 1px solid #419eff;
    text-align: center;
  }
}
</style>
