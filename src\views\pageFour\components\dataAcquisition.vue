<template>
  <div class="dataAcquisition">
    <div class="headerImg">数据汇集</div>
    <div class="bottomClass">
      <div class="data-acquisition">
        <div class="data-acquisition-header">
          累计采集数据：
          <span class="highlight">{{ totalDataNum }}</span>
          亿条
          <span class="highlight2">{{ dataResourceNum }}</span>
          个数据资源
        </div>
        <div class="data-acquisition-content">
          <div class="data-acquisition-row">
            <div
              class="data-acquisition-card"
              v-for="card in pagedCards"
              :key="card.title"
              :style="
                card.title === '商业采买'
                  ? {
                      width: '100%',
                      'background-size': '100%  5.75521vw',
                    }
                  : {}
              "
            >
              <div class="card-title">{{ card.title }}</div>
              <div class="card-info">
                <div>
                  <div class="info-label">{{ card.leftLabel }}</div>
                  <div class="info-value">{{ card.leftValue }}</div>
                </div>
                <div class="info-divider"></div>
                <div
                  :style="
                    card.title === '商业采买'
                      ? {
                          position: 'relative',
                          left: '1.09375vw',
                        }
                      : {}
                  "
                >
                  <div class="info-label">{{ card.rightLabel }}</div>
                  <div class="info-value">{{ card.rightValue }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="page-btns">
            <button @click="prevPage">＜</button>
            <button @click="nextPage">＞</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { loadStyle } from "@/util/util";
import { queryRealtimeMetricList } from "@/api/article.js";
const DEFAULT_CARDS = [
  {
    title: "四川省大数据中心-四川站",
    leftLabel: "数据资源",
    leftValue: "54个",
    rightLabel: "对接委办局",
    rightValue: "12个",
  },
  {
    title: "四川省大数据中心-增信平台",
    leftLabel: "数据资源",
    leftValue: "80个",
    rightLabel: "对接委办局",
    rightValue: "17个",
  },
  {
    title: "商业采买",
    leftLabel: "数据资源",
    leftValue: "8393个",
    rightLabel: "数据",
    rightValue: "39.87亿条",
  },
  // {
  //   title: "蚂蚁区块链科技",
  //   leftLabel: "数据资源",
  //   leftValue: "106个",
  //   rightLabel: "数据",
  //   rightValue: "20.89亿条",
  // },
  // {
  //   title: "成都金控征信",
  //   leftLabel: "数据资源",
  //   leftValue: "8287个",
  //   rightLabel: "数据",
  //   rightValue: "18.98亿条",
  // },
  // 还可以继续添加更多卡片
];

export default {
  data() {
    return {
      cards: [...DEFAULT_CARDS],
      currentPage: 0,
      maxDt: localStorage.getItem("maxDt"),
      totalDataNum: 0, // 累计采集数据
      dataResourceNum: 0, // 数据资源总数
    };
  },
  mounted() {
    this.init();
  },
  computed: {
    pagedCards() {
      const start = this.currentPage * 2;
      return this.cards.slice(start, start + 2);
    },
  },
  methods: {
    async init() {
      try {
        const res = await queryRealtimeMetricList([
          {
            bizDate: this.maxDt,
            dimTime: "ALL",
            dimIndustry: "ALL",
            dimCounty: "ALL",
            dimPark: "ALL",
            dimIndustryChain: "ALL",
            indexName: "数据汇集",
          },
        ]);

        if (res.data?.code === 200) {
          const apiData = res.data.data;
          // console.log("原始JSON字符串:", JSON.stringify(apiData[0].bizContent));
          if (apiData?.length > 0) {
            try {
              const bizContent = JSON.parse(apiData[0].bizContent || "{}");
              // 设置数据
              this.totalDataNum = bizContent.total_data_num || 0;
              this.dataResourceNum = bizContent.data_resource_num || 0;
              this.cards = Array.isArray(bizContent.cards)
                ? bizContent.cards
                : [];
            } catch (e) {
              console.error("JSON解析失败:", e);
              this.setDefaultValues();
            }
          } else {
            this.setDefaultValues();
          }
        } else {
          this.setDefaultValues();
        }
      } catch (error) {
        console.error("接口请求失败:", error);
        this.setDefaultValues();
      }
    },
    setDefaultValues() {
      this.totalDataNum = 0;
      this.dataResourceNum = 0;
      this.cards = [...DEFAULT_CARDS];
    },
    prevPage() {
      if (this.currentPage > 0) this.currentPage--;
    },
    nextPage() {
      if ((this.currentPage + 1) * 2 < this.cards.length) this.currentPage++;
    },
  },
};
</script>
<style scoped lang="scss">
.dataAcquisition {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: cover;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  }
  .bottomClass {
    width: 100%;
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 1px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    display: flex;
    justify-content: space-around;
    align-items: center;
    backdrop-filter: blur(7px);
    .data-acquisition {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      line-height: 40px;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      .data-acquisition-header {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 28px;
        color: #ffffff;
        letter-spacing: 1px;
        text-align: center;
        font-style: normal;
        .highlight {
          letter-spacing: 2px;
          text-align: center;
          font-family: OPPOSans, OPPOSans;
          font-weight: bold;
          font-size: 46px;
          letter-spacing: 2px;
          // text-shadow: 0px 0px 4px rgba(255,185,49,0.47), 0px 0px 14px rgba(255,189,65,0.54), 0px 2px 4px rgba(0,0,0,0.5);
          background: linear-gradient(90deg, #ffffff 0%, #ffce7c 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
        .highlight2 {
          font-family: OPPOSans, OPPOSans;
          font-weight: bold;
          font-size: 44px;
          color: #ffffff;
          line-height: 70px;
          letter-spacing: 2px;
          text-align: center;
          font-style: normal;
          text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
          background: linear-gradient(180deg, #ffffff 0%, #7cebff 100%);
          background-clip: text;
          -webkit-text-fill-color: transparent;
        }
      }
      .data-acquisition-content {
        .data-acquisition-row {
          display: flex;
          justify-content: space-evenly;
          margin-bottom: 20px;
          margin-top: 20px;
          .data-acquisition-card {
            background: url("../assets/caiji.png");
            background-size: 415px 221px;
            background-repeat: no-repeat;
            width: 415px;
            // width: 100%;
            height: 221px;
            display: flex;
            flex-direction: column;
            align-items: center;
            .card-title {
              width: 415px;
              // width: 100%;
              height: 95px;
              line-height: 95px;
              font-family: PingFangSC, PingFang SC;
              font-weight: 600;
              font-size: 26px;
              color: #ffffff;
              letter-spacing: 1px;
              text-shadow: 0px 0px 11px #4187ff, 0px 1px 2px rgba(0, 0, 0, 0.5);
              text-align: center;
              font-style: normal;
            }
            .card-info {
              display: flex;
              flex-direction: row;
              gap: 42px;
              > div {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .info-label {
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 400;
                  font-size: 22px;
                  color: #81b6fd;
                  line-height: 60px;
                  letter-spacing: 1px;
                  text-align: center;
                  font-style: normal;
                  // left: 42px;
                }
                .info-value {
                  font-family: PingFangSC, PingFang SC;
                  font-weight: 600;
                  font-size: 34px;
                  color: #ffffff;
                  line-height: 30px;
                  letter-spacing: 1px;
                  text-align: center;
                  font-style: normal;
                  .arrow-up {
                    font-family: PingFangSC, PingFang SC;
                    font-weight: 400;
                    font-size: 22px;
                    color: #ffffff;
                    line-height: 30px;
                    letter-spacing: 1px;
                    text-align: center;
                    font-style: normal;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}
.page-btns {
  display: flex;
  justify-content: center;
  margin-top: 30px;
  button {
    width: 40px;
    height: 40px;
    margin: 0 8px;
    border: none;
    background: #0a4d8f;
    color: #addcff;
    font-size: 20px;
    cursor: pointer;
    &:disabled {
      cursor: not-allowed;
    }
    &:hover {
      background: #0a4d8f;
      color: #addcff;
      font-size: 22px;
      font-weight: 600;
    }
  }
}
.data-acquisition-row {
  display: flex;
  justify-content: center;
  gap: 24px;
}
</style>
