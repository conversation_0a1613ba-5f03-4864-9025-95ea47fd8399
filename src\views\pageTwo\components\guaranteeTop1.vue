<template>
  <div class="guaranteeTop1-guaranteeTop1">
    <div class="guaranteeTop1-headerImg">
      <div class="guaranteeTop1-lbClass">机构放款担保方式Top1</div>
    </div>
    <div class="guaranteeTop1-bottomClass">
      <div class="guaranteeTop1-guarantee-grid">
        <div
          class="guaranteeTop1-guarantee-card"
          v-for="item in guaranteeList"
          :key="item.type"
        >
          <div class="guaranteeTop1-guarantee-type">
            <img
              :src="item.img"
              alt=""
              class="guaranteeTop1-guarantee-type-img"
            />
          </div>
          <div class="guaranteeTop1-guarantee-info">
            <div class="guaranteeTop1-bank-name">{{ item.bank }}</div>
            <div style="display: flex">
              <div class="guaranteeTop1-amount">
                {{ item.amount }}<span class="guaranteeTop1-unit"></span>
              </div>
              <div class="guaranteeTop1-count">
                {{ item.count }}<span class="guaranteeTop1-unit"></span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdateList } from "@/api/article.js";
export default {
  name: "fkyhlb",
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      classOption: {
        step: 0.2, // 数值越大速度滚动越快
        limitMoveNum: 4, // 开始无缝滚动的数据量 this.dataList.length
        hoverStop: true, // 是否开启鼠标悬停stop
        direction: 1, // 0向下 1向上 2向左 3向右
        openWatch: true, // 开启数据实时监控刷新dom
        singleHeight: 50, // 单步运动停止的高度(默认值0是无缝不停止的滚动) direction => 0/1
        singleWidth: 0, // 单步运动停止的宽度(默认值0是无缝不停止的滚动) direction => 2/3
        waitTime: 1000, // 单步运动停止的时间(默认值1000ms)
      },
      teeList: [
        {
          type: "信用",
          img: require("../assets/Creditloan.png"),
          bank: "建设银行",
          amount: "1904.78",
          count: 123,
        },
        {
          type: "保证",
          img: require("../assets/Guaranteeloan.png"),
          bank: "建设银行",
          amount: "1904.78",
          count: 123,
        },
        {
          type: "质押",
          img: require("../assets/Mortgage.png"),
          bank: "建设银行",
          amount: "1904.78",
          count: 123,
        },
        {
          type: "抵押",
          img: require("../assets/Pledgeloan.png"),
          bank: "建设银行",
          amount: "1904.78",
          count: 123,
        },
      ],
      guaranteeList: [],
    };
  },
  mounted() {
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "金融机构放款担保结构",
      },
    ]).then((res) => {
      /*         console.log(res,'金融机构放款担保结构'); */
      let list = JSON.parse(res.data.data[0].bizContent);
      this.guaranteeList = list.map((item, index) => {
        return {
          type: this.teeList[index].type,
          bank: item.shortName,
          amount: item.loanAmt,
          count: item.loanSum,
          img: this.teeList[index].img,
        };
      });
      console.log(this.guaranteeList, "金融机构放款担保结构");
      this.guaranteeList[1].bank = list[2].shortName;
      this.guaranteeList[1].amount = list[2].loanAmt;
      this.guaranteeList[1].count = list[2].loanSum;
      this.guaranteeList[2].bank = list[1].shortName;
      this.guaranteeList[2].amount = list[1].loanAmt;
      this.guaranteeList[2].count = list[1].loanSum;
      this.init();
    });
  },
  methods: {
    init() {},
    showConModal(e) {
      const path = e.path || (e.composedPath && e.composedPath());
      let target = path.filter((r) => /picShow/.test(r.className));
      if (target.length) target = target[0];
      else return;
      const data = JSON.parse(target.getAttribute("data"));
      if (data) {
        //点击事件处理逻辑
      }
    },
    handleScroll(e) {
      this.$refs.vueSeamlessScroll1.yPos =
        this.$refs.vueSeamlessScroll1.yPos - e.deltaY + 60;
      // 如果是正数 说明是往上滚
      if (this.$refs.vueSeamlessScroll1.yPos > 0) {
        this.$refs.vueSeamlessScroll1.yPos = 0;
        return;
      }
    },
  },
};
</script>
<style scope="scoped" lang="scss">
.guaranteeTop1-guaranteeTop1 {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;
  .guaranteeTop1-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    .guaranteeTop1-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-left: -80px;
      .guaranteeTop1-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }
      .guaranteeTop1-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }
  .guaranteeTop1-bottomClass {
    height: calc(100% - 80px);
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);

    .guaranteeTop1-guarantee-grid {
      height: calc(100% - 80px);
      display: flex;
      flex-wrap: nowrap;
      justify-content: space-around;
      flex-direction: column;
      align-items: flex-start;
      padding: 32px 32px;
    }
    .guaranteeTop1-guarantee-card {
      margin-top: 20px;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      // width: 404px;
      width: 100%;
      height: 90px;
      background: rgba(0, 51, 102, 0.18);
      border-radius: 10px;
      box-shadow: 0 0 8px #0a7bbd33;
      padding: 10px;
    }
    .guaranteeTop1-guarantee-type {
      width: 70px;
      height: 70px;
      border: 2px dashed #7cebff;
      border-radius: 8px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-left: 18px;
      margin-right: 18px;
      background: rgba(10, 29, 56, 0.5);
    }
    .guaranteeTop1-type-title {
      color: #fff;
      font-size: 22px;
      font-weight: bold;
      margin-bottom: 2px;
      letter-spacing: 2px;
    }
    .guaranteeTop1-type-sub {
      color: #7cebff;
      font-size: 16px;
      font-weight: 400;
    }
    .guaranteeTop1-guarantee-info {
      margin-left: 10px;
      display: flex;
      flex-direction: column;
      justify-content: center;
    }
    .guaranteeTop1-bank-name {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #ffffff;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }
    .guaranteeTop1-amount {
      font-family: OPPOSans, OPPOSans;
      font-weight: normal;
      font-size: 32px;
      color: #ffffff;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      background: linear-gradient(90deg, #ffffff 0%, #f3c376 100%);
      width: 180px;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      // left: -220px;
    }
    .guaranteeTop1-count {
      font-family: OPPOSans, OPPOSans;
      font-weight: normal;
      font-size: 32px;
      color: #ffffff;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      background: linear-gradient(90deg, #ffffff 0%, #4da3ff 100%);
      width: 150px;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
    .guaranteeTop1-unit {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 22px;
      color: #ffffff;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
    }
  }
}

.guaranteeTop1-guarantee-type-img {
  width: 108px;
  height: 108px;
  display: block;
}
</style>
