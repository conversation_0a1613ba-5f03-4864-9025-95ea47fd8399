<!--
  放款企业行业分布组件
  功能：
  1. 展示放款企业的行业分布情况
  2. 提供行业数据的柱状图可视化
  3. 支持数据自动滚动展示
  4. 提供详情弹窗查看完整数据
-->
<template>
  <div class="fwstjjClass">
    <!-- 头部区域：标题和更多按钮 -->
    <div class="headerImg">
      <div class="lbClass">放款企业行业分布</div>
      <div class="rightClass">
        <div class="djgdClass" @click="handleClick">点击查看更多</div>
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <!-- 底部内容区域：展示行业数据 -->
    <div class="bottomClass">
      <!-- 头部统计信息 -->
      <div class="header">
        放款资金投放行业：<span class="total">{{industries.length}}</span> 个
      </div>
      <!-- 行业数据柱状图容器 -->
      <div class="industryBar_box">
        <div class="industryBar" ref="industryBar"></div>
      </div>
    </div>
    <!-- 详情弹窗 -->
    <DialogBar
      :visible="visible"
      @close="handleClose"
      title="放款企业行业分布"
      :chartData="originalData"
      :chart-width="1000"
      :chart-height="468"
      :colorStops="colorStops"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import DialogBar from "../../components/dialogBar.vue";
import { productUpdateList } from "@/api/article.js";

export default {
  name: "sjcpFour",
  data() {
    return {
      visible: false, // 控制弹窗显示
      // 图表渐变色配置
      colorStops: [
        { offset: 0, color: "#41FCFF" },
        { offset: 1, color: "#57BDFF" },
      ],
      myChart: null, // ECharts实例
      scrollTimer: null, // 自动滚动定时器
      currentIndex: 0, // 当前滚动索引
      scrollSpeed: 3000, // 滚动间隔时间（毫秒）
      scrollStep: 1, // 每次滚动的条数
      originalData: [], // 原始数据
      // 行业列表
      industries: [
        "批发和零售业",
        "居民服务、修理和其他服务业",
        "农、林、牧、渔业",
        "科学研究和技术服务业",
        "信息传输、软件和信息技术服务业",
        "租赁和商务服务业",
        "建筑业",
        "交通运输、仓储和邮政业",
        "住宿和餐饮业",
        "教育",
        "房地产业",
        "水利、环境和公共设施管理业",
        "卫生和社会工作",
        "文化、体育和娱乐业",
        "制造业",
        "电力、热力、燃气及水生产和供应业",
        "采矿业",
        "金融业"
      ]
    };
  },
  components: {
    DialogBar
  },
  mounted() {
    this.initData();
  },
  beforeDestroy() {
    // 组件销毁前清理定时器和图表实例
    if (this.scrollTimer) {
      clearInterval(this.scrollTimer);
    }
    if (this.myChart) {
      this.myChart.dispose();
    }
  },
  methods: {
    // 打开详情弹窗
    handleClick() {
      this.visible = true;
    },
    // 关闭详情弹窗
    handleClose() {
      this.visible = false;
    },
    // 初始化数据
    async initData() {
      // 构建金额查询参数
      let obj = this.industries.map(item => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: item,
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: 'ALL',
        indexName: "放款行业金额",
      }));

      // 构建笔数查询参数
      let obj2 = this.industries.map(item => ({
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: item,
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: 'ALL',
        indexName: "放款行业笔数",
      }));

      // 合并查询参数
      let obj3 = obj.concat(obj2);
      try {
        // 获取数据
        const res = await productUpdateList(obj3);
        let list = res.data.data;
        // 过滤金额数据
        let list1 = list.filter(item => item.indexName === "放款行业金额");
        // 过滤笔数数据
        let list2 = list.filter(item => item.indexName === "放款行业笔数");
        
        // 处理数据格式
        list1.map((item, index) => {
          item.value = item.indexValue - 0;
          item.name = item.dimIndustry;
          item.count = list2.find(d => d.dimIndustry === item.dimIndustry).indexValue - 0;
        });

        this.originalData = list1;
        this.originalData.sort((a, b) => a.value - b.value);
        
        await this.$nextTick();
        this.initBar();
      } catch (error) {
        console.error('获取数据失败:', error);
      }
    },
    // 初始化柱状图
    initBar() {
      if (this.myChart) {
        this.myChart.dispose();
      }

      const chartRef = this.$refs.industryBar;
      if (!chartRef) {
        console.warn('Chart container not found, retrying...');
        setTimeout(() => this.initBar(), 10000);
        return;
      }

      this.myChart = echarts.init(chartRef);
      const data = [...this.originalData].sort((a, b) => a.value - b.value);
      let maxValue = Math.max(...data.map((d) => Number(d.value)));
      maxValue = maxValue * 1.6;

      // 配置图表选项
      this.myChart.setOption({
        // 图表网格配置
        grid: { 
          left: this.$autoFontSize(150), 
          right: this.$autoFontSize(30), 
          top: this.$autoFontSize(0), 
          bottom: this.$autoFontSize(30) 
        },
        // 提示框配置
        tooltip: {
          trigger: 'axis',
          axisPointer: { type: 'shadow' },
          formatter: function(params) {
            const data = params[0];
            if (data.name && data.name.length > 9) {
              return data.name;
            }
            return '';
          },
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          borderColor: 'transparent',
          textStyle: {
            color: '#fff',
            fontSize: this.$autoFontSize(14)
          },
          extraCssText: 'padding: 8px 12px;'
        },
        // X轴配置
        xAxis: {
          type: "value",
          show: false,
          max: maxValue, 
        },
        // Y轴配置
        yAxis: {
          type: "category",
          data: this.originalData.map((item) => item.name),
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: "#fff",
            padding: [this.$autoFontSize(5), 0, 0, 0],
            fontSize: this.$autoFontSize(12),
            formatter: function(value) {
              if (value.length > 9) {
                return value.substring(0, 9) + '...';
              }
              return value;
            },
            rich: {
              a: {
                color: '#fff',
                fontSize: this.$autoFontSize(12),
                lineHeight: this.$autoFontSize(20)
              }
            }
          },
        },
        // 系列配置
        series: [
          // 背景条
          {
            type: "bar",
            data: data.map(() => maxValue),
            barWidth: this.$autoFontSize(22),
            itemStyle: {
              color: "rgba(63, 169, 245, 0.2)",
              borderRadius: 0,
            },
            barGap: "-80%",
            z: 1,
          },
          // 主数据条
          {
            type: "bar",
            data: data.map((item) => item.value),
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#41FCFF" },
                  { offset: 1, color: "#57BDFF" },
                ],
              },
              borderRadius: 0,   
            },
            label: {
              show: true,
              position: "right",
              formatter: (params) => {
                const d = data[params.dataIndex];
                return `${d.value}亿元/${d.count}笔`;
              },
              color: "#fff",
              fontSize: this.$autoFontSize(14),
            },
          },
          // 白色标记点
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "rect",
            symbolSize: [this.$autoFontSize(4), this.$autoFontSize(16)],
            symbolOffset: [0, 3],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: data.map((item, idx) => [Number(item.value), idx]),
          },
        ],
      });

      // 启动自动滚动
      this.startAutoScroll(data.length);

      // 监听窗口大小变化
      window.addEventListener("resize", () => this.myChart.resize());
    },
    // 启动自动滚动
    startAutoScroll(totalItems) {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }
      this.scrollTimer = setInterval(() => {
        const option = this.myChart.getOption();
        const currentData = option.yAxis[0].data;

        // 更新当前索引
        this.currentIndex = (this.currentIndex + this.scrollStep) % totalItems;

        // 重新排序数据
        const newData = [...currentData];
        for (let i = 0; i < this.scrollStep; i++) {
          newData.unshift(newData.pop())
        }
        // 重新排序原始数据
        this.originalData = newData.map((n) => {
          if (this.originalData.find((d) => d.name == n)) {
            const item = this.originalData.find((d) => d.name == n)
            return item
          }
        })  
        // 重新计算最大值
        let maxValue = Math.max(...this.originalData.map((d) => Number(d.value)));
        maxValue = maxValue * 1.5;

        // 更新图表
        this.myChart.setOption({
          yAxis: {
            data: newData,
          },
          xAxis: {
            max: maxValue,
          },
          series: [
            // 背景条
            {
              data: newData.map(() => maxValue),
            },
            // 主数据
            {
              data: newData.map((name) => {
                const item = this.originalData.find((d) => d.name === name);
                return item ? item.value : 0;
              }),          
              label: {
                show: true,
                position: "right",
                formatter: (params) => {
                  const d = this.originalData[params.dataIndex];
                  return `${d.value}亿元/${d.count}笔`;
                },
              },
            },
            // 白色长条
            {
              data: newData.map((name, idx) => {
                const item = this.originalData.find((d) => d.name === name);
                return [item ? Number(item.value) : 0, idx];
              }),
            },
          ],
        });
      }, this.scrollSpeed);
    },
  },
};
</script>

<style scoped lang="scss">
/* 主容器样式 */
.fwstjjClass {
  width: 950px;
  height: 27vh;
  display: flex;
  flex-direction: column;
  
  /* 头部区域样式 */
  .headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: left;
    padding-left: 90px;
    box-sizing: border-box;
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    
    /* 标题样式 */
    .lbClass {
      height: 80px;
      line-height: 80px;
    }
    
    /* 右侧按钮样式 */
    .rightClass {
      display: flex;
      align-items: center;
      justify-content: center;
      
      .djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }
      
      .imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }
  
  /* 图表容器样式 */
  .industryBar_box {
    height: 1200px;
  }
  
  .industryBar {
    width: 940px;
    height: 1200px;
  }
  
  /* 底部内容区域样式 */
  .bottomClass {
    flex: 1;
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    overflow: hidden;
    
    /* 头部统计信息样式 */
    .header {
      font-family: PingFangSC, PingFang SC;
      font-weight: 400;
      font-size: 28px;
      color: #FFFFFF;
      line-height: 40px;
      letter-spacing: 1px;
      text-align: left;
      font-style: normal;
      margin-left: 00px;
      width: 90%;
    }   
    
    /* 总数样式 */
    .total {
      font-family: OPPOSans, OPPOSans;
      font-weight: normal;
      font-size: 44px;
      color: #FFFFFF;
      line-height: 70px;
      letter-spacing: 2px;
      text-align: left;
      font-style: normal;
      text-shadow: 0px 2px 2px rgba(0, 0, 0, 0.1);
      background: linear-gradient(180deg, #ffffff 0%, #7cebff 100%);
      font-weight: bold;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }
}
</style>
