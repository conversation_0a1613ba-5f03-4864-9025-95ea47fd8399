<!-- 
  产品放款金额Top10组件
  功能：展示金融机构产品放款金额排名前10的数据可视化图表
  特点：
  1. 支持自动滚动展示数据
  2. 包含累计放款金额和笔数统计
  3. 使用ECharts实现柱状图展示
  4. 支持弹窗查看详细数据
  5. 响应式设计，支持窗口大小变化
-->
<template>
  <div class="AmountTop10-fwstjjClass">
    <!-- 头部区域：标题和查看更多按钮 -->
    <div class="AmountTop10-headerImg">
      <div class="AmountTop10-lbClass">产品放款Top20</div>
      <div class="AmountTop10-rightClass">
        <!-- 查看更多按钮：点击触发弹窗显示详细数据 -->
        <div class="AmountTop10-djgdClass" @click="handleClick">
          点击查看更多
        </div>
        <div>
          <img src="@/assets/dp/rightT.png" class="AmountTop10-imgRight" />
        </div>
      </div>
    </div>
    <!-- 主体内容区域：统计数据和图表展示 -->
    <div class="AmountTop10-bottomClass">
      <!-- 顶部统计信息：显示累计放款金额和笔数 -->
      <div class="AmountTop5-top-summary">
        <span>累计放款金额：</span>
        <span class="AmountTop5-highlight">{{ amountTotal }}</span
        >亿元
        <span class="AmountTop5-highlight2">{{ parseInt(countTotal) }}</span
        >笔
      </div>
      <!-- 图表容器：使用ECharts渲染柱状图 -->
      <div class="AmountTop10-industryBar_box">
        <div class="AmountTop10-industryBar" ref="industryBar"></div>
      </div>
    </div>
    <!-- 弹窗组件：用于展示详细数据，支持自定义宽度和高度 -->
    <DialogBar
      :visible="visible"
      @close="handleClose"
      title="产品放款Top20"
      :chartData="originalData"
      :chart-width="1000"
      :chart-height="468"
    />
  </div>
</template>

<script>
import * as echarts from "echarts";
import DialogBar from "@/views/components/dialogBar.vue";
import { productUpdateList } from "@/api/article.js";

export default {
  name: "AmountTop10",
  mounted() {
    this.initBar();
  },
  data() {
    return {
      myChart: null, // ECharts实例，用于管理图表生命周期
      scrollTimer: null, // 自动滚动定时器，控制数据轮播
      currentIndex: 0, // 当前滚动索引，用于追踪显示位置
      scrollSpeed: 3000, // 滚动间隔时间（毫秒），控制轮播速度
      scrollStep: 1, // 每次滚动的条数，控制轮播步长
      originalData: [], // 原始数据，存储从API获取的完整数据
      chartData: [], // 图表数据，用于ECharts渲染
      visible: false, // 弹窗显示状态，控制DialogBar组件的显示/隐藏
      amountTotal: "", // 累计放款金额，用于顶部统计显示
      countTotal: "", // 累计放款笔数，用于顶部统计显示
    };
  },
  mounted() {
    // 获取产品放款金额数据
    // 请求三个指标：金融机构产品放款金额、累计放款金额、累计放款笔数
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "金融机构产品放款金额",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 0,
        dimIndustryChain: "ALL",
        indexName: "累计放款金额",
      },
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 1,
        dimIndustryChain: "ALL",
        indexName: "累计放款笔数",
      },
    ]).then((res) => {
      // 处理返回的数据
      let list = JSON.parse(res.data.data[0].bizContent);
      this.amountTotal = res.data.data[1].indexValue;
      this.countTotal = res.data.data[2].indexValue;

      // 格式化数据用于图表显示
      // 将API返回的数据转换为ECharts所需的格式
      this.originalData = list.map((item) => {
        return {
          name: item.shortName + "\n" + item.proName, // 组合显示机构名称和产品名称
          value: item.loanAmt.replace("亿元", ""), // 移除单位，保留数值
          percent: item.ratio, // 保存占比数据
        };
      });
      this.chartData = JSON.parse(JSON.stringify(this.originalData)).sort(
        (a, b) => a.value - b.value
      ); // 深拷贝原始数据，避免修改原始数据
      this.initBar();
    });
  },
  components: {
    DialogBar, // 注册弹窗组件
  },
  methods: {
    // 关闭弹窗：重置visible状态
    handleClose() {
      this.visible = false;
    },
    // 打开弹窗：设置visible状态为true
    handleClick() {
      this.visible = true;
    },
    // 组件销毁前清理资源：清除定时器和图表实例
    beforeDestroy() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }
      if (this.myChart) {
        this.myChart.dispose();
      }
    },
    // 初始化柱状图：配置ECharts选项并渲染图表
    initBar() {
      // 初始化ECharts实例
      this.myChart = echarts.init(this.$refs.industryBar);

      // 对数据进行排序并计算最大值
      const data = this.chartData.slice(0, 4);
      let maxValue = Math.max(...data.map((d) => Number(d.value)));
      maxValue = maxValue * 1.5; // 设置最大值为实际最大值的1.5倍，留出显示空间

      // 配置图表选项
      this.myChart.setOption({
        // 设置图表边距，使用自适应字体大小
        grid: {
          left: this.$autoFontSize(120),
          right: this.$autoFontSize(50),
          top: this.$autoFontSize(10),
          bottom: this.$autoFontSize(10),
        },
        // 配置X轴：隐藏刻度，设置最大值
        xAxis: {
          type: "value",
          show: false,
          max: maxValue,
        },
        // 配置Y轴：显示类别名称，自定义样式
        yAxis: {
          type: "category",
          data: data.map((item) => item.name),
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: "#fff",
            fontSize: this.$autoFontSize(14),
            // 每10个字符换行，优化显示效果
            formatter: function (value) {
              return value.replace(/(.{10})/g, "$1\n");
            },
          },
        },
        series: [
          // 背景条：作为进度条的底色
          {
            type: "bar",
            data: data.map(() => maxValue),
            barWidth: this.$autoFontSize(22),
            itemStyle: {
              color: "#00326B",
              borderRadius: 0,
            },
            barGap: "-80%", // 设置条形图之间的间距
            z: 1, // 设置图层顺序
          },
          // 主数据条：显示实际数值
          {
            type: "bar",
            data: data.map((item) => item.value),
            barWidth: this.$autoFontSize(14),
            itemStyle: {
              // 设置渐变色
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: [
                  { offset: 0, color: "#41CCFF" },
                  { offset: 1, color: "#5785FF" },
                ],
              },
              borderRadius: 0,
            },
            // 配置数据标签
            label: {
              show: true,
              position: "right",
              formatter: (params) => {
                const d = this.chartData.find(
                  (item) => item.name === params.name
                );
                try {
                  return `${d.value}亿元/${d.percent}`;
                } catch (error) {
                  return "";
                }
              },
              color: "#fff",
              fontSize: this.$autoFontSize(14),
            },
          },
          // 白色标记点：作为数据条的装饰
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "rect",
            symbolSize: [this.$autoFontSize(4), this.$autoFontSize(16)],
            symbolOffset: [0, 3],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: data.map((item, idx) => [Number(item.value), idx]),
          },
        ],
      });

      // 启动自动滚动
      this.startAutoScroll(data.length);
      // 监听窗口大小变化，自动调整图表大小
      window.addEventListener("resize", () => this.myChart.resize());
    },
    // 启动自动滚动功能：实现数据轮播效果
    startAutoScroll(totalItems) {
      // 清除已存在的定时器
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
      }

      // 设置新的定时器，定期更新数据
      this.scrollTimer = setInterval(() => {
        if (!this.chartData) return;
        let newData = [];
        if (this.chartData) {
          this.chartData.push(this.chartData.shift());
          // 重新排序数据：将最后一条数据移到最前面
          newData = this.chartData.slice(0, 4);
        }
        // for (let i = 0; i < this.scrollStep; i++) {
        //   newData.unshift(newData.pop());
        // }

        // 重新计算最大值
        let maxValue = Math.max(...newData.map((d) => Number(d.value)));
        maxValue = maxValue * 1.5;

        // 配置图表选项
        this.myChart.setOption({
          // 设置图表边距，使用自适应字体大小
          grid: {
            left: this.$autoFontSize(120),
            right: this.$autoFontSize(50),
            top: this.$autoFontSize(2),
            bottom: this.$autoFontSize(5),
          },
          // 配置X轴：隐藏刻度，设置最大值
          xAxis: {
            type: "value",
            show: false,
            max: maxValue,
          },
          // 配置Y轴：显示类别名称，自定义样式
          yAxis: {
            type: "category",
            data: newData.map((item) => item.name),
            axisLine: { show: false },
            axisTick: { show: false },
            axisLabel: {
              color: "#fff",
              fontSize: this.$autoFontSize(14),
              // 每10个字符换行，优化显示效果
              formatter: function (value) {
                return value.replace(/(.{10})/g, "$1\n");
              },
            },
            barCategoryGap: "40%", // 设置柱状图之间的间距
          },
          series: [
            // 背景条：作为进度条的底色
            {
              type: "bar",
              data: newData.map(() => maxValue),
              barWidth: this.$autoFontSize(22),
              itemStyle: {
                color: "#00326B",
                borderRadius: 0,
              },
              barGap: "-80%", // 设置条形图之间的间距
              z: 1, // 设置图层顺序
            },
            // 主数据条：显示实际数值
            {
              type: "bar",
              data: newData.map((item) => item.value),
              barWidth: this.$autoFontSize(12),
              itemStyle: {
                // 设置渐变色
                color: {
                  type: "linear",
                  x: 0,
                  y: 0,
                  x2: 1,
                  y2: 0,
                  colorStops: [
                    { offset: 0, color: "#41CCFF" },
                    { offset: 1, color: "#5785FF" },
                  ],
                },
                borderRadius: 0,
              },
              // 配置数据标签
              label: {
                show: true,
                position: "right",
                formatter: (params) => {
                  const d = newData.find((item) => item.name === params.name);
                  try {
                    return `${d.value}亿元/${d.percent}`;
                  } catch (error) {
                    return "";
                  }
                },
                color: "#fff",
                fontSize: this.$autoFontSize(14),
              },
            },
            // 白色标记点：作为数据条的装饰
            {
              type: "scatter",
              coordinateSystem: "cartesian2d",
              symbol: "rect",
              symbolSize: [this.$autoFontSize(4), this.$autoFontSize(16)],
              symbolOffset: [0, 3],
              itemStyle: {
                color: "#fff",
                shadowColor: "#fff",
              },
              z: 3,
              data: newData.map((item, idx) => [Number(item.value), idx]),
            },
          ],
        });
      }, this.scrollSpeed);
    },
  },
};
</script>

<style scoped lang="scss">
/* 组件主容器样式 */
.AmountTop10-fwstjjClass {
  width: 950px;
  height: 25vh;
  display: flex;
  flex-direction: column;

  /* 头部区域样式 */
  .AmountTop10-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    // 添加文字阴影效果
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;

    /* 标题样式 */
    .AmountTop10-lbClass {
      height: 80px;
      line-height: 80px;
    }

    /* 右侧按钮区域样式 */
    .AmountTop10-rightClass {
      display: flex;
      align-items: center;
      justify-content: center;

      /* 查看更多按钮样式 */
      .AmountTop10-djgdClass {
        font-family: PingFangSC, PingFang SC;
        font-weight: 400;
        font-size: 26px;
        color: #77c1ff;
        letter-spacing: 1px;
        cursor: pointer;
        margin-right: 12px;
      }

      /* 箭头图标样式 */
      .AmountTop10-imgRight {
        width: 12px;
        height: 22px;
        position: relative;
        top: -1px;
      }
    }
  }

  .AmountTop10-industryBar {
    width: 930px;
    // height: 100px;
    height: 100%;
    // padding-top: 16px;
    // position: relative;
    // top: 16px;
  }

  /* 底部内容区域样式 */
  .AmountTop10-bottomClass {
    flex: 1 1 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
    backdrop-filter: blur(7px);
    overflow: hidden;

    /* 自定义滚动条样式 */
    &::-webkit-scrollbar {
      width: 8px;
      background: #0a2e4a;
      border-radius: 4px;
    }
    &::-webkit-scrollbar-thumb {
      background: linear-gradient(180deg, #2b9cff 0%, #398fff 100%);
      border-radius: 4px;
    }
    &::-webkit-scrollbar-track {
      background: #0a2e4a;
      border-radius: 4px;
    }
  }

  /* 顶部统计信息样式 */
  .AmountTop5-top-summary {
    flex: 0 0 auto;
    width: 940px;
    text-align: left;
    font-size: 26px;
    color: #fff;
    margin-left: 60px;

    /* 高亮金额样式：使用渐变文字效果 */
    .AmountTop5-highlight {
      background: linear-gradient(180deg, #fff 0%, #ffce7c 100%);
      font-family: OPPOSans, OPPOSans;
      font-weight: bold;
      font-size: 44px;
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }

    /* 高亮笔数样式：使用渐变文字效果 */
    .AmountTop5-highlight2 {
      margin-left: 20px;
      font-family: OPPOSans, OPPOSans;
      font-weight: bold;
      font-size: 44px;
      background: linear-gradient(180deg, #fff 0%, #7cebff 100%);
      background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  }

  /* 图表容器样式 */
  .AmountTop10-industryBar_box {
    // height: 900px;
    flex: 1 1 0;
    overflow: hidden;
  }
}
</style>
