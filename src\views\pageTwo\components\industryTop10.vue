<!-- 
  银行笔均放款金额和利率对比组件
  功能：展示各银行放款金额和利率的对比数据
  特点：
  1. 使用ECharts实现双Y轴图表展示
  2. 支持自动滚动展示数据
  3. 支持弹窗查看详细数据
  4. 响应式设计，支持窗口大小变化
  5. 包含金额柱状图和利率折线图
-->
<template>
  <div class="industryTop1-szyyClassFour">
    <!-- 头部区域：标题和查看更多按钮 -->
    <div class="industryTop1-headerImg">
      <div>机构笔均放款金额 & 利率对比</div>
      <div class="LendingExtreme-rightClass">
        <div class="djgdClass" @click="openDialogMore">点击查看更多</div>
        <div>
          <img src="@/assets/dp/rightT.png" class="imgRight" />
        </div>
      </div>
    </div>
    <!-- 主体内容区域：图表展示 -->
    <div class="industryTop1-bottomClass">
      <div ref="bankChart" class="industryTop1-bank-chart"></div>
    </div>
    <!-- 弹窗组件：用于展示详细数据 -->
    <div v-if="dialogVisible" class="dialogBar-model" @click="handleMaskClick">
      <div class="dialogBar-content" @click.stop>
        <div class="dialogBar-header-box">
          <div class="dialogBar-header">
            <div class="dialogBar-titleBox">
              <span class="dialogBar-title">机构笔均放款金额 & 利率对比</span>
            </div>
          </div>
        </div>
        <div class="dialogBar-body">
          <div class="dialogBar-chart" ref="dialogChartRef"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import vueSeamlessScroll from "vue-seamless-scroll";
import { productUpdateList, queryRealtimeMetricList } from "@/api/article.js";
import * as echarts from "echarts";

export default {
  name: "industryTOP1",
  components: {
    vueSeamlessScroll,
  },
  data() {
    return {
      flag: true,
      listData: [], // 列表数据
      scrollTimer: null, // 自动滚动定时器
      currentIndex: 0, // 当前滚动索引
      rrObj: {
        qaNum: "",
        sessionNum: "",
        userAcccess: "",
        useNum: "",
      },
      RealtimeQuery: "",
      RealtimeAvg: "",
      // 银行名称列表
      bankNames: [
        "中国\n建设银行",
        "中国\n农业银行",
        "成都\n银行",
        "中国\n工商银行",
        "中国\n银行",
        "中国\n交通银行",
      ],
      barData: [150, 80, 120, 130, 110, 90], // 放款金额数据（万元）
      lineData: [13, 23, 13, 23, 13, 23], // 利率数据（百分比）
      // 标签数据：包含百分比和金额
      labelData: [
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
        { percent: "23%", amount: "130万" },
      ],
      dialogVisible: false, // 弹窗显示状态
      dialogChart: null, // 弹窗图表实例
    };
  },
  mounted() {
    // 获取放款数据均值
    productUpdateList([
      {
        bizDate: localStorage.getItem("maxDt"),
        dimTime: "ALL",
        dimIndustry: "ALL",
        dimCounty: "ALL",
        dimPark: "ALL",
        dimNum: 2,
        dimIndustryChain: "ALL",
        indexName: "放款数据均值",
      },
    ]).then((res) => {
      // 处理返回的数据
      let list = JSON.parse(res.data.data[0].bizContent);
      this.bankNames = list.map((item) => item.shortName);
      this.barData = list.map((item) => item.loanAmt.replace("万元", ""));
      this.lineData = list.map((item) => item.avgRate.replace("%", ""));
      this.labelData = list.map((item) => {
        return {
          percent: item.avgRate,
          amount: item.loanAmt,
        };
      });
      this.initBankChart();
      this.startAutoScroll();
    });
  },
  beforeDestroy() {
    // 组件销毁前清理资源
    this.stopAutoScroll();
    if (this.bankChart) {
      this.bankChart.dispose();
    }
    if (this.dialogChart) {
      this.dialogChart.dispose();
    }
    window.removeEventListener(
      "resize",
      () => this.dialogChart && this.dialogChart.resize()
    );
  },
  methods: {
    // 打开弹窗并初始化弹窗图表
    openDialogMore() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.initDialogChart();
      });
    },
    // 处理弹窗遮罩点击事件
    handleMaskClick() {
      this.dialogVisible = false;
      if (this.dialogChart) {
        this.dialogChart.dispose();
        this.dialogChart = null;
      }
    },
    // 初始化弹窗图表
    initDialogChart() {
      if (!this.$refs.dialogChartRef) {
        console.warn("Dialog chart container not found");
        return;
      }

      const container = this.$refs.dialogChartRef;

      // 等待DOM更新完成
      this.$nextTick(() => {
        if (container.offsetWidth === 0 || container.offsetHeight === 0) {
          console.warn("Chart container has no size, retrying...");
          setTimeout(() => this.initDialogChart(), 100);
          return;
        }

        try {
          if (this.dialogChart) {
            this.dialogChart.dispose();
          }

          this.dialogChart = echarts.init(container);
          // 计算最大值，用于设置背景条
          let maxValue = Math.max(...this.barData.map(Number));
          maxValue = maxValue * 1.2;

          // 配置图表选项
          const option = {
            // 设置图表边距
            grid: {
              left: this.$autoFontSize(40),
              right: this.$autoFontSize(40),
              top: this.$autoFontSize(40),
              bottom: this.$autoFontSize(20),
            },
            tooltip: { show: false },
            // 配置X轴
            xAxis: {
              type: "category",
              data: this.bankNames,
              axisTick: {
                show: false, // 隐藏刻度线
              },
              axisLabel: {
                color: "#fff",
                fontSize: this.$autoFontSize(14),
                interval: 0,
                width: this.$autoFontSize(100),
                overflow: "break",
                formatter: function (value) {
                  return value.replace(/(.{6})/g, "$1\n");
                },
              },
              axisLine: { lineStyle: { color: "rgba(255,255,255,0.2)" } },
            },
            // 配置双Y轴
            yAxis: [
              {
                type: "value",
                name: "单位：万元",
                nameTextStyle: {
                  color: "#fff",
                  fontSize: this.$autoFontSize(14),
                },
                axisLabel: { color: "#fff", fontSize: this.$autoFontSize(14) },
                splitLine: { lineStyle: { color: "rgba(122,192,255,0.15)" } },
              },
              {
                type: "value",
                name: "单位：%",
                min: 0,
                max: 25,
                interval: 5,
                nameTextStyle: {
                  color: "#fff",
                  fontSize: this.$autoFontSize(14),
                },
                axisLabel: {
                  color: "#FFFFFF",
                  fontSize: this.$autoFontSize(14),
                  formatter: "{value}%",
                },
                splitLine: { show: false },
              },
            ],
            // 配置数据系列
            series: [
              // 背景条
              {
                name: "背景",
                type: "bar",
                data: this.barData.map(() => maxValue),
                barWidth: this.$autoFontSize(12),
                itemStyle: {
                  color: "rgba(255,255,255,0.08)",
                  borderRadius: 6,
                },
                barGap: "-100%",
                z: 1,
              },
              // 金额柱状图
              {
                name: "金额",
                type: "bar",
                data: this.barData,
                barWidth: this.$autoFontSize(12),
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: "#ffe7b0" },
                      { offset: 0.5, color: "#f3c376" },
                      { offset: 1, color: "rgba(243,195,118,0)" },
                    ],
                  },
                },
                label: {
                  show: true,
                  position: "top",
                  formatter: (params) => {
                    const label = this.labelData[params.dataIndex];
                    return `{a|${label.amount}}\n{b|${label.percent}}`;
                  },
                  rich: {
                    a: {
                      color: "#23eaff",
                      fontSize: this.$autoFontSize(14),
                      padding: [0, 0, this.$autoFontSize(5), 0],
                    },
                    b: { color: "#ffce7c", fontSize: this.$autoFontSize(14) },
                  },
                  align: "center",
                },
                z: 3,
              },
              // 利率折线图
              {
                name: "利率",
                type: "line",
                yAxisIndex: 1,
                data: this.lineData,
                symbol: "circle",
                symbolSize: 0,
                lineStyle: { color: "#23eaff", width: 2 },
                itemStyle: {
                  color: "#23eaff",
                  borderColor: "#fff",
                  borderWidth: 2,
                },
                z: 4,
              },
              // 白色标记点
              {
                type: "scatter",
                symbol: "rect",
                symbolSize: [this.$autoFontSize(18), this.$autoFontSize(4)],
                itemStyle: {
                  color: "#fff",
                  shadowColor: "#fff",
                },
                z: 5,
                data: this.barData.map((val, idx) => [idx, Number(val)]),
              },
            ],
          };

          this.dialogChart.setOption(option);
          window.addEventListener(
            "resize",
            () => this.dialogChart && this.dialogChart.resize()
          );
        } catch (error) {
          console.error("Failed to initialize dialog chart:", error);
        }
      });
    },
    // 启动自动滚动
    startAutoScroll() {
      this.scrollTimer = setInterval(() => {
        if (this.bankChart) {
          this.currentIndex = (this.currentIndex + 1) % this.bankNames.length;
          this.bankChart.setOption({
            dataZoom: [
              {
                startValue: this.currentIndex,
                endValue: this.currentIndex + 3,
              },
            ],
          });
        }
      }, 3000);
    },
    // 停止自动滚动
    stopAutoScroll() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
    },
    // 初始化主图表
    initBankChart() {
      if (!this.$refs.bankChart) {
        console.warn("Bank chart container not found");
        return;
      }

      this.$nextTick(() => {
        try {
          const chart = echarts.init(this.$refs.bankChart);
          // 计算最大值，用于设置背景条
          let maxValue = Math.max(...this.barData.map(Number));
          maxValue = maxValue * 1.2;

          // 配置图表选项
          const option = {
            grid: {
              left: this.$autoFontSize(40),
              right: this.$autoFontSize(40),
              top: this.$autoFontSize(50),
              bottom: this.$autoFontSize(20),
            },
            tooltip: { show: false },
            // 配置数据缩放
            dataZoom: [
              {
                type: "slider",
                show: false,
                xAxisIndex: [0],
                startValue: 0,
                endValue: 3,
              },
            ],
            // 配置X轴
            xAxis: {
              type: "category",
              data: this.bankNames,
              axisLabel: {
                color: "#fff",
                fontSize: this.$autoFontSize(14),
                interval: 0,
              },
              axisLine: { lineStyle: { color: "rgba(255,255,255,0.2)" } },
            },
            // 配置双Y轴
            yAxis: [
              {
                type: "value",
                name: "单位：万元",
                nameTextStyle: {
                  color: "#fff",
                  fontSize: this.$autoFontSize(14),
                },
                axisLabel: { color: "#fff", fontSize: this.$autoFontSize(14) },
                splitLine: { lineStyle: { color: "rgba(122,192,255,0.15)" } },
              },
              {
                name: "单位：%",
                type: "value",
                min: 0,
                max: 25,
                interval: 5,
                nameTextStyle: {
                  color: "#fff",
                  fontSize: this.$autoFontSize(14),
                },
                axisLabel: {
                  color: "#FFFFFF",
                  fontSize: this.$autoFontSize(14),
                  formatter: "{value}%",
                },
                splitLine: { show: false },
              },
            ],
            // 配置数据系列
            series: [
              // 背景条
              {
                name: "背景",
                type: "bar",
                data: this.barData.map(() => maxValue),
                barWidth: this.$autoFontSize(12),
                itemStyle: {
                  color: "rgba(255,255,255,0.08)",
                  borderRadius: 6,
                },
                barGap: "-100%",
                z: 1,
              },
              // 金额柱状图
              {
                name: "金额",
                type: "bar",
                data: this.barData,
                barWidth: this.$autoFontSize(12),
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: "#ffe7b0" },
                      { offset: 0.5, color: "#f3c376" },
                      { offset: 1, color: "rgba(243,195,118,0)" },
                    ],
                  },
                },
                label: {
                  show: true,
                  position: "top",
                  formatter: (params) => {
                    const label = this.labelData[params.dataIndex];
                    return `{a|${label.amount}}\n{b|${label.percent}}`;
                  },
                  rich: {
                    a: {
                      color: "#ffce7c",
                      fontSize: this.$autoFontSize(14),
                      padding: [0, 0, this.$autoFontSize(5), 0],
                    },
                    b: { color: "#23eaff", fontSize: this.$autoFontSize(14) },
                  },
                  align: "center",
                },
                z: 3,
              },
              // 利率折线图
              {
                name: "利率",
                type: "line",
                yAxisIndex: 1,
                data: this.lineData,
                symbol: "circle",
                symbolSize: 0,
                lineStyle: { color: "#23eaff", width: 2 },
                itemStyle: {
                  color: "#23eaff",
                  borderColor: "#fff",
                  borderWidth: 2,
                },
                z: 4,
              },
              // 白色标记点
              {
                type: "scatter",
                symbol: "rect",
                symbolSize: [this.$autoFontSize(18), this.$autoFontSize(4)],
                itemStyle: {
                  color: "#fff",
                  shadowColor: "#fff",
                },
                z: 5,
                data: this.barData.map((val, idx) => [idx, Number(val)]),
              },
            ],
          };
          chart.setOption(option);
          this.bankChart = chart;
          window.addEventListener(
            "resize",
            () => this.bankChart && this.bankChart.resize()
          );
        } catch (error) {
          console.error("Failed to initialize bank chart:", error);
        }
      });
    },
  },
};
</script>

<style scope="scoped" lang="scss">
/* 组件主容器样式 */
.industryTop1-szyyClassFour {
  width: 940px;
  height: 27vh;
  display: flex;
  flex-direction: column;

  /* 头部区域样式 */
  .industryTop1-headerImg {
    width: 940px;
    height: 80px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-left: 90px;
    box-sizing: border-box;
    // 设置背景图片
    background-image: url("~@/assets/dp/headerbg.png");
    background-repeat: no-repeat;
    background-size: 940px 80px;
    font-family: YouSheBiaoTiHei;
    font-size: 42px;
    color: #ffffff;
    letter-spacing: 2px;
    // 添加文字阴影效果
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
  }

  /* 底部内容区域样式 */
  .industryTop1-bottomClass {
    width: 100%;
    flex: 1;
    // 设置渐变背景
    background: linear-gradient(
      180deg,
      rgba(3, 29, 58, 0) 0%,
      rgba(0, 50, 107, 0.64) 100%
    );
    border: 2px solid;
    border-top: 0;
    // 设置边框渐变
    border-image: linear-gradient(
        169deg,
        rgba(44, 110, 162, 0),
        rgba(47, 115, 169, 1)
      )
      2 2;
  }
}

/* 右侧按钮区域样式 */
.LendingExtreme-rightClass {
  display: flex;
  align-items: center;
  justify-content: center;

  /* 查看更多按钮样式 */
  .djgdClass {
    font-family: PingFangSC, PingFang SC;
    font-weight: 400;
    font-size: 26px;
    color: #77c1ff;
    letter-spacing: 1px;
    cursor: pointer;
    margin-right: 12px;
  }

  /* 箭头图标样式 */
  .imgRight {
    width: 12px;
    height: 22px;
    position: relative;
    top: -1px;
  }
}

/* 图表容器样式 */
.industryTop1-bank-chart {
  width: 100%;
  height: 22vh;
}

/* 弹窗样式 */
.dialogBar-model {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 弹窗内容样式 */
.dialogBar-content {
  background: url("~@/assets/dp/dialog.png");
  background-size: 100% 100%;
  border-radius: 12px;
  width: 1930.79px;
  height: 1152.11px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}

/* 弹窗头部样式 */
.dialogBar-header-box {
  display: flex;
}

.dialogBar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 33px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 弹窗主体样式 */
.dialogBar-body {
  padding: 37px 48px 0 48px;
}

/* 弹窗图表容器样式 */
.dialogBar-chart {
  width: 100%;
  min-height: 900px;
  background: rgba(0, 24, 48, 0.1);
}

/* 弹窗标题样式 */
.dialogBar-titleBox {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  .dialogBar-title {
    padding-left: 50px;
    width: 314px;
    height: 89px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    margin-left: 50px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
  }
}
</style>
