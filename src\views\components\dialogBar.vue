<!--
  水平柱状图弹窗组件
  功能：
  1. 展示数据的水平柱状图
  2. 支持自定义图表尺寸
  3. 支持自适应字体大小
  4. 支持自定义渐变色
  5. 支持数据标签显示（金额/笔数）
  6. 支持窗口大小自适应
-->
<template>
  <!-- 弹窗遮罩层 -->
  <div v-if="visible" class="dialogBar-model" @click="handleMaskClick">
    <!-- 弹窗内容区 -->
    <div class="dialogBar-content" @click.stop>
      <!-- 弹窗头部 -->
      <div class="dialogBar-header-box">
        <div class="dialogBar-header">
          <div class="dialogBar-titleBox">
            <span class="dialogBar-title">{{ title }}</span>
          </div>
        </div>
      </div>
      <!-- 弹窗主体 -->
      <div class="dialogBar-body">
        <!-- 数据为空提示 -->
        <div v-if="chartData.length === 0" class="no-data-container">
          <!-- <img src="@/assets/no-data.png" class="no-data-icon"/> -->
          <div class="no-data-text">暂无数据</div>
        </div>
        <!-- 图表容器，支持自定义尺寸 -->
        <div
          v-else
          class="dialogBar-chart"
          ref="chartRef"
          :style="{
            width: this.$autoFontSize(chartWidth),
            height: this.$autoFontSize(chartHeight),
            background: 'rgba(0, 24, 48, 0.1)',
          }"
        ></div>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";

export default {
  name: "DialogBar",
  // 组件数据
  data() {
    return {
      chart: null, // echarts实例
    };
  },
  // 组件属性定义
  props: {
    visible: {
      type: Boolean,
      default: false, // 控制弹窗显示
    },
    title: {
      type: String,
      default: "模型应用", // 弹窗标题
    },
    chartData: {
      type: Array,
      default: () => [], // 图表数据
    },
    chartHeight: {
      type: [String, Number],
      default: 1068, // 图表高度
    },
    chartWidth: {
      type: [String, Number],
      default: 1471, // 图表宽度
    },
    colorStops: {
      type: Array,
      default: () => [
        // 柱状图渐变色配置
        { offset: 0, color: "#41CCFF" },
        { offset: 1, color: "#5785FF" },
      ],
    },
  },
  // 监听器
  watch: {
    // 监听弹窗显示状态
    visible(val) {
      if (val) {
        this.$nextTick(() => {
          setTimeout(() => {
            this.initChart();
          }, 100);
        });
      }
    },
    // 监听数据变化
    chartData: {
      handler(val) {
        if (this.chart && val.length) {
          this.updateChart();
        }
      },
      deep: true,
    },
  },
  methods: {
    // 关闭弹窗
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    // 处理遮罩层点击
    handleMaskClick() {
      this.close();
    },
    // 初始化图表
    initChart() {
      if (!this.$refs.chartRef) return;

      const container = this.$refs.chartRef;
      // 检查容器尺寸，如果为0则重试
      if (container.offsetWidth === 0 || container.offsetHeight === 0) {
        console.warn("Chart container has no size, retrying...");
        setTimeout(() => this.initChart(), 1000);
        return;
      }

      // 如果已存在图表实例，先销毁
      if (this.chart) {
        this.chart.dispose();
      }

      // 创建新的图表实例
      this.chart = echarts.init(container);
      this.updateChart();
      // 添加窗口大小变化监听
      window.addEventListener("resize", this.handleResize);
    },
    // 更新图表数据
    updateChart() {
      if (!this.chart) return;
      // 对数据进行排序
      const data = [...this.chartData].sort((a, b) => a.value - b.value);

      // 处理数据中的换行符
      data.forEach((item) => {
        if (item.name && item.name.includes("\n")) {
          item.name1 = item.name.replace(/\n/g, "    ");
        } else {
          item.name1 = item.name;
        }
      });

      // 计算最大值，并增加30%的空间
      let maxValue = Math.max(...data.map((d) => Number(d.value)));
      maxValue = maxValue * 1.3;

      // 配置图表选项
      this.chart.setOption({
        // 图表网格配置
        grid: {
          left: this.$autoFontSize(300),
          right: this.$autoFontSize(50),
          top: this.$autoFontSize(0),
          bottom: this.$autoFontSize(30),
        },
        // X轴配置（值轴）
        xAxis: {
          type: "value",
          show: false,
          max: maxValue,
        },
        // Y轴配置（类目轴）
        yAxis: {
          type: "category",
          data: data.map((item) => item.name1),
          axisLine: { show: false },
          axisTick: { show: false },
          axisLabel: {
            color: "#fff",
            align: "right",
            padding: [this.$autoFontSize(5), 0, 0, 10],
            fontSize: this.$autoFontSize(14),
            rich: {
              a: {
                align: "right",
                verticalAlign: "middle",
                lineHeight: this.$autoFontSize(22),
                fontSize: this.$autoFontSize(14),
                color: "#fff",
                fontFamily: "inherit",
              },
            },
          },
        },
        // 数据系列配置
        series: [
          // 背景柱状图
          {
            type: "bar",
            data: data.map(() => maxValue),
            barWidth: this.$autoFontSize(22),
            itemStyle: {
              color: "rgba(63, 169, 245, 0.2)",
              borderRadius: 0,
            },
            barGap: "-77%",
            z: 1,
          },
          // 主数据柱状图
          {
            type: "bar",
            data: data.map((item) => item.value),
            barWidth: this.$autoFontSize(12),
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 1,
                y2: 0,
                colorStops: this.colorStops,
              },
              borderRadius: 0,
            },
            // 标签配置
            label: {
              show: true,
              position: "right",
              formatter: (params) => {
                const d = data[params.dataIndex];
                if (d.percent) {
                  return `${d.value}亿元/${d.percent}`;
                } else if (d.count) {
                  return `${d.value}亿元/${d.count}笔`;
                } else {
                  return `${d.value}`;
                }
              },
              color: "#fff",
              fontSize: this.$autoFontSize(14),
            },
          },
          // 装饰性散点图
          {
            type: "scatter",
            coordinateSystem: "cartesian2d",
            symbol: "rect",
            symbolSize: [this.$autoFontSize(4), this.$autoFontSize(16)],
            symbolOffset: [0, 2.5],
            itemStyle: {
              color: "#fff",
              shadowColor: "#fff",
            },
            z: 3,
            data: data.map((item, idx) => [Number(item.value), idx]),
          },
        ],
      });
    },
    // 处理窗口大小变化
    handleResize() {
      this.chart && this.chart.resize();
    },
  },
  // 生命周期钩子
  mounted() {
    if (this.visible) {
      this.$nextTick(() => {
        setTimeout(() => {
          this.initChart();
        }, 100);
      });
    }
  },
  beforeDestroy() {
    // 清理资源
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
    window.removeEventListener("resize", this.handleResize);
  },
};
</script>

<style scoped lang="scss">
/* 弹窗遮罩层样式 */
.dialogBar-model {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  width: 100%;
  height: 100%;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 标题样式 */
.dialogBar-titleBox {
  width: 940px;
  background-image: url("~@/assets/dp/headerbg.png");
  background-size: 940px 89px;
  background-repeat: no-repeat;
  height: 89px;
  .dialogBar-title {
    padding-left: 50px;
    width: 314px;
    height: 89px;
    font-family: YouSheBiaoTiHei;
    font-weight: 200;
    font-size: 42px;
    color: #ffffff;
    line-height: 89px;
    margin-left: 50px;
    letter-spacing: 2px;
    text-shadow: 0px 0px 9px rgba(0, 175, 255, 0.68), 0px 2px 4px #000c1a;
    text-align: left;
    font-style: normal;
  }
}

/* 弹窗内容区样式 */
.dialogBar-content {
  background: url("~@/assets/dp/dialog.png");
  background-size: 100% 100%;
  border-radius: 12px;
  width: 1930.79px;
  height: 1352.11px;
  box-shadow: 0 0 30px #0a1a2a;
  position: relative;
  padding: 0;
}

/* 弹窗头部样式 */
.dialogBar-header-box {
  display: flex;
}

.dialogBar-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30px 24px 0 24px;
  font-size: 20px;
  color: #fff;
  font-weight: bold;
  position: relative;
}

/* 弹窗主体样式 */
.dialogBar-body {
  padding: 37px 48px 0 48px;
}

/* 图表容器样式 */
.dialogBar-chart {
  min-height: 1200px;
  background: rgba(0, 24, 48, 0.1);
}
.no-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // height: 595px;
  height: 1352.11px;
  width: 100%;
}

.no-data-text {
  color: #909399;
  font-size: 24px;
  letter-spacing: 2px;
  line-height: 30px;
}
</style>
